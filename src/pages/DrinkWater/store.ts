import { atom } from 'jotai';
import { queryWaterInfo, QueryWaterInfoResponse } from 'services/welfare/drinkWater';

// 喝水信息原子
export const waterInfoAtom = atom<QueryWaterInfoResponse | null>(null);

// 加载状态原子
export const waterInfoLoadingAtom = atom<boolean>(false);

// 写入喝水信息的原子
export const writeWaterInfoAtom = atom(
  null,
  async (get, set) => {
    set(waterInfoLoadingAtom, true);
    try {
      const response = await queryWaterInfo();
      if (response?.data) {
        set(waterInfoAtom, response.data);
      }
    } catch (error) {
      console.error('Failed to fetch water info:', error);
    } finally {
      set(waterInfoLoadingAtom, false);
    }
  }
);
