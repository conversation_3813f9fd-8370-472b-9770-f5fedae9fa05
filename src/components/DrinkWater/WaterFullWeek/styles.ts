import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');
const containerPadding = 16;
const dayItemMargin = 14;
const dayItemWidth = (width - 4 * containerPadding - 6 * dayItemMargin) / 7;
const dayItemHeight = (39 / 32) * dayItemWidth;

export const getStyles = () => StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    paddingHorizontal: containerPadding,
    paddingVertical:20,
    marginHorizontal: 16,
    marginTop: 20
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 7
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#131415',
  },
  subTitle: {
    fontSize: 12,
    color: '#999999',
  },
  subTitleDay: {
   fontSize: 12,
   color: '#59A8FF',
   paddingHorizontal: 2
  },
  daysWrapper: {
    justifyContent: 'center',
    marginBottom: 20,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    zIndex: 2,
  },
  progressBarContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: dayItemHeight + 4,
    height: 8,
    zIndex: 1,
  },
  progressTrack: {
    position: 'absolute',
    left: dayItemWidth / 2 - 10,
    right: dayItemWidth / 2 - 10,
    top: 3,
    height: 2,
    backgroundColor: '#D2E9FF',
    borderRadius: 1,
  },
  progressBar: {
    position: 'absolute',
    left: dayItemWidth / 2 - 10,
    top: 3,
    height: 2,
    backgroundColor: '#77B7F5',
    borderRadius: 1,
  },
  progressNodesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: '100%',
  },
  progressNodeWrapper: {
    width: dayItemWidth,
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressNode: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressNodeInner: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#D2E9FF',
  },
  progressNodeCompleted: {
    backgroundColor: '#77B7F5',
  },
  dayContainer: {
    alignItems: 'center'
  },
  dayImage: {
    width: dayItemWidth,
    height: dayItemHeight,
    resizeMode: 'contain',
    marginBottom: 20,
  },
  dayText: {
    fontSize: 11,
    color: '#B3D5F5',
  },
  dayTextCompleted: {
    color: '#404850'
  },
  tips: {
    fontSize: 11,
    color: '#999999'
  },
});
